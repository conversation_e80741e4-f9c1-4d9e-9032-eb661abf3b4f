# Dashboard Screen

## Overview
The Dashboard Screen is a comprehensive order management interface for the Packagingwala web application. It provides a visual overview of all order statuses and stages in the packaging workflow.

## Features

### Layout
- **Responsive Design**: Adapts to different screen sizes with dynamic grid columns
- **Sidebar Navigation**: Fixed sidebar with logo and navigation menu
- **Main Content Area**: Header with welcome message and search functionality, plus dashboard grid

### Dashboard Cards
The dashboard displays 15 different order status cards:

1. **Total Orders** (45) - Overall order count
2. **On-boarding** (5) - New orders being processed
3. **Designing** (5) - Orders in design phase
4. **Sampling** (6) - Sample creation stage
5. **Design Plate Approved** (5) - Approved design plates
6. **Cylinder Development** (5) - Cylinder creation process
7. **Polyester Sample Approved** (5) - Approved polyester samples
8. **Polyester Printing** (1) - Printing on polyester
9. **Lamination** (1) - Lamination process
10. **Metallised Pasting** (1) - Metallised pasting stage
11. **Heating** (1) - Heating process
12. **Curing** (1) - Curing stage
13. **Zipper Addition** (1) - Adding zippers
14. **Slitting** (1) - Slitting process
15. **Pouching** (1) - Creating pouches
16. **Sorting** (1) - Sorting orders
17. **Packing** (1) - Packing stage
18. **Ready to Dispatch** (1) - Ready for shipping
19. **Dispatched** (1) - Completed orders

### Icons
Each card uses custom PNG icons from the `assets/icons/` folder, displayed with the SmartIcon widget for consistent styling and color theming.

### Responsive Behavior
- **Large screens (1200px+)**: 4 columns
- **Medium screens (800-1200px)**: 3 columns  
- **Small screens (<800px)**: 2 columns

## Usage

### Navigation
To navigate to the dashboard screen:
```dart
Navigator.pushNamed(context, '/dashboard');
```

### Customization
The dashboard cards can be easily customized by modifying the `_buildDashboardGrid()` method in `lib/screens/dashboard_screen.dart`.

### Search Functionality
The search bar is functional and allows users to search through orders (search logic can be implemented as needed).

## File Structure
```
lib/
├── screens/
│   └── dashboard_screen.dart          # Main dashboard implementation
├── widgets/
│   ├── smart_svg_icon.dart           # Smart icon widget
│   └── platform_icon.dart           # Platform-specific icons
├── constants/
│   ├── app_colors.dart               # Color definitions
│   └── size.dart                     # Responsive sizing
└── main.dart                         # App entry point with routes

assets/
└── icons/                           # Dashboard card icons
    ├── onboarding_icon.png
    ├── designing_icon.png
    ├── sampling_icon.png
    └── ... (other status icons)

test/
└── dashboard_screen_test.dart        # Unit tests
```

## Testing
Run the dashboard tests with:
```bash
flutter test test/dashboard_screen_test.dart
```

The tests cover:
- Basic rendering and layout
- Responsive behavior
- Search functionality
- Card display and scrolling

## Dependencies
- Flutter Material Design
- Custom SmartIcon widget for icon handling
- Responsive sizing system (MySize)
- Custom color scheme (AppColors)
