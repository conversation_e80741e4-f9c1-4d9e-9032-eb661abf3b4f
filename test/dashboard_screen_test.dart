import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:packagingwala_web/screens/dashboard_screen.dart';
import 'package:packagingwala_web/controllers/dashboard_controller.dart';
import 'package:packagingwala_web/constants/app_colors.dart';

void main() {
  group('DashboardScreen Tests', () {
    setUp(() {
      // Initialize GetX controller for testing
      Get.put(DashboardController());
    });

    tearDown(() {
      // Clean up GetX controllers after each test
      Get.reset();
    });

    testWidgets('Dashboard screen renders correctly', (WidgetTester tester) async {
      // Set a larger screen size for testing
      await tester.binding.setSurfaceSize(const Size(1200, 800));

      // Build the dashboard screen
      await tester.pumpWidget(
        GetMaterialApp(
          home: const DashboardScreen(),
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primaryColor,
              primary: AppColors.primaryColor,
              surface: AppColors.backgroundColor,
            ),
            useMaterial3: true,
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the welcome text is displayed
      expect(find.text('Welcome Back Krishna'), findsOneWidget);
      expect(find.text("Let's get your orders moving"), findsOneWidget);

      // Verify that the search bar is present
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search Orders'), findsOneWidget);

      // Verify that the sidebar is present
      expect(find.text('Packagingwala'), findsOneWidget);
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Customers List'), findsOneWidget);
      expect(find.text('Orders List'), findsOneWidget);

      // Verify that dashboard cards are present
      expect(find.text('Total Orders'), findsOneWidget);
      expect(find.text('45'), findsOneWidget);
      expect(find.text('On-boarding'), findsOneWidget);
      expect(find.text('Designing'), findsOneWidget);
      expect(find.text('Sampling'), findsOneWidget);
    });

    testWidgets('Dashboard grid displays cards', (WidgetTester tester) async {
      // Set a larger screen size for testing
      await tester.binding.setSurfaceSize(const Size(1200, 800));

      await tester.pumpWidget(
        GetMaterialApp(
          home: const DashboardScreen(),
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primaryColor,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify some key dashboard cards (not all may be visible in viewport)
      expect(find.text('Total Orders'), findsOneWidget);
      expect(find.text('On-boarding'), findsOneWidget);
      expect(find.text('Designing'), findsOneWidget);
      expect(find.text('Sampling'), findsOneWidget);

      // Scroll down to see more cards
      await tester.drag(find.byType(GridView), const Offset(0, -300));
      await tester.pumpAndSettle();

      // Check for cards that should be visible after scrolling
      expect(find.text('Lamination'), findsOneWidget);
      expect(find.text('Heating'), findsOneWidget);
    });

    testWidgets('Search bar is functional', (WidgetTester tester) async {
      // Set a larger screen size for testing
      await tester.binding.setSurfaceSize(const Size(1200, 800));

      await tester.pumpWidget(
        GetMaterialApp(
          home: const DashboardScreen(),
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primaryColor,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Find the search text field
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);

      // Enter text in the search field
      await tester.enterText(searchField, 'test search');
      await tester.pump();

      // Verify the text was entered
      expect(find.text('test search'), findsOneWidget);
    });

    testWidgets('Responsive layout works', (WidgetTester tester) async {
      // Test with smaller screen size
      await tester.binding.setSurfaceSize(const Size(600, 800));

      await tester.pumpWidget(
        GetMaterialApp(
          home: const DashboardScreen(),
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primaryColor,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the layout still works with smaller screen
      expect(find.text('Welcome Back Krishna'), findsOneWidget);
      expect(find.text('Total Orders'), findsOneWidget);
      expect(find.byType(GridView), findsOneWidget);
    });
  });
}
