import 'dart:developer';

import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/platform_icon.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _agreeToPrivacyPolicy = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  double getResponsiveWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      // Mobile
      return screenWidth * 0.9;
    } else if (screenWidth < 1024) {
      // Tablet
      return screenWidth * 0.7;
    } else if (screenWidth < 1440) {
      // Laptop
      return screenWidth * 0.5;
    } else {
      // Desktop
      return screenWidth * 0.35;
    }
  }

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.05,
                vertical: MySize.size24,
              ),
              child: Container(
                width: getResponsiveWidth(context),
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size200,
                  vertical: MySize.size28,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(MySize.size16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: MySize.size20),

                    Image.asset(
                      'assets/png/logo.png',
                      height: MySize.size80,
                      width: MySize.size200,
                      fit: BoxFit.contain,
                    ),

                    SizedBox(height: MySize.size40),

                    Text(
                      'Welcome Back',
                      style: TextStyle(
                        fontSize: MySize.size28,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),

                    SizedBox(height: MySize.size8),

                    Text(
                      'Log in to manage your orders effortlessly.',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: MySize.size40),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Phone Number',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: MySize.size8),
                        CustomTextField(
                          controller: _phoneController,
                          hintText: 'Enter Your Phone Number',
                          keyboardType: TextInputType.phone,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(MySize.size12),
                            child: PlatformIcon(
                              iconName: 'phone',
                              size: MySize.size20,
                              color: AppColors.greyColor,
                            ),
                          ),
                          fillColor: Colors.white,
                          borderRadius: MySize.size12,
                        ),
                      ],
                    ),

                    SizedBox(height: MySize.size20),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Password',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: MySize.size8),
                        CustomTextField(
                          controller: _passwordController,
                          hintText: 'Enter Your Password',
                          obscureText: true,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(MySize.size12),
                            child: PlatformIcon(
                              iconName: 'lock',
                              size: MySize.size20,
                              color: AppColors.greyColor,
                            ),
                          ),
                          fillColor: Colors.white,
                          borderRadius: MySize.size12,
                        ),
                      ],
                    ),

                    SizedBox(height: MySize.size16),

                    Row(
                      children: [
                        Checkbox(
                          value: _agreeToPrivacyPolicy,
                          onChanged: (value) {
                            setState(() {
                              _agreeToPrivacyPolicy = value ?? false;
                            });
                          },
                          activeColor: AppColors.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'I agree with Privacy and Policy',
                            style: TextStyle(
                              fontSize: MySize.size14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: MySize.size32),

                    SizedBox(
                      width: double.infinity,
                      height: MySize.size50,
                      child: ElevatedButton(
                        onPressed: _agreeToPrivacyPolicy ? _handleLogin : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          disabledBackgroundColor: AppColors.greyColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(MySize.size12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Login',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: MySize.size24),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Don't have an account? ",
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        GestureDetector(
                          onTap: _handleSignUp,
                          child: Text(
                            'Sign up',
                            style: TextStyle(
                              fontSize: MySize.size14,
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: MySize.size20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleLogin() {
    log('Login pressed with phone: ${_phoneController.text}');
  }

  void _handleSignUp() {
    log('Sign up pressed');
  }
}
