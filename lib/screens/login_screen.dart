import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/platform_icon.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _agreeToPrivacyPolicy = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Top spacing
                SizedBox(height: MySize.size60),

                // Logo
                Image.asset(
                  'assets/png/logo.png',
                  height: MySize.size80,
                  width: MySize.size200,
                  fit: BoxFit.contain,
                ),

                SizedBox(height: MySize.size40),

                // Welcome Back title
                Text(
                  'Welcome Back',
                  style: TextStyle(
                    fontSize: MySize.size28,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),

                SizedBox(height: MySize.size8),

                // Subtitle
                Text(
                  'Log in to manage your orders effortlessly.',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: MySize.size40),

                // Phone Number field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Phone Number',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: MySize.size8),
                    CustomTextField(
                      controller: _phoneController,
                      hintText: 'Enter Your Phone Number',
                      keyboardType: TextInputType.phone,
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(MySize.size12),
                        child: PlatformIcon(
                          iconName: 'phone',
                          size: MySize.size20,
                          color: AppColors.greyColor,
                        ),
                      ),
                      fillColor: Colors.white,
                      borderRadius: MySize.size12,
                    ),
                  ],
                ),

                SizedBox(height: MySize.size20),

                // Password field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Password',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: MySize.size8),
                    CustomTextField(
                      controller: _passwordController,
                      hintText: 'Enter Your Password',
                      obscureText: true,
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(MySize.size12),
                        child: PlatformIcon(
                          iconName: 'lock',
                          size: MySize.size20,
                          color: AppColors.greyColor,
                        ),
                      ),
                      fillColor: Colors.white,
                      borderRadius: MySize.size12,
                    ),
                  ],
                ),

                SizedBox(height: MySize.size16),

                // Privacy Policy checkbox
                Row(
                  children: [
                    Checkbox(
                      value: _agreeToPrivacyPolicy,
                      onChanged: (value) {
                        setState(() {
                          _agreeToPrivacyPolicy = value ?? false;
                        });
                      },
                      activeColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'I agree with Privacy and Policy',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: MySize.size32),

                // Login Button
                SizedBox(
                  width: double.infinity,
                  height: MySize.size50,
                  child: ElevatedButton(
                    onPressed: _agreeToPrivacyPolicy ? _handleLogin : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      disabledBackgroundColor: AppColors.greyColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Login',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                SizedBox(height: MySize.size24),

                // Sign up link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Don't have an account? ",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    GestureDetector(
                      onTap: _handleSignUp,
                      child: Text(
                        'Sign up',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: MySize.size40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleLogin() {
    // TODO: Implement login logic
    print('Login pressed with phone: ${_phoneController.text}');
  }

  void _handleSignUp() {
    // TODO: Navigate to sign up screen
    print('Sign up pressed');
  }
}