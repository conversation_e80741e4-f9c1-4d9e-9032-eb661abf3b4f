import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/platform_icon.dart';
import '../controllers/dashboard_controller.dart';

class DashboardScreen extends GetView<DashboardController> {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: Row(
        children: [
          // Left Sidebar
          _buildSidebar(),

          // Main Content
          Expanded(
            child: _buildMainContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: 200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Padding(
            padding: EdgeInsets.all(MySize.size24),
            child: Column(
              children: [
                // Logo
                Container(
                  width: MySize.size60,
                  height: MySize.size60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(MySize.size12),
                  ),
                  child: Center(
                    child: Text(
                      'P',
                      style: TextStyle(
                        fontSize: MySize.size32,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: MySize.size12),
                Text(
                  'Packagingwala',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(() => Column(
              children: [
                _buildNavItem(
                  icon: 'home',
                  label: 'Dashboard',
                  isSelected: controller.selectedNavIndex.value == 0,
                  onTap: () => controller.selectNavItem(0),
                ),
                _buildNavItem(
                  icon: 'person',
                  label: 'Customers List',
                  isSelected: controller.selectedNavIndex.value == 1,
                  onTap: () => controller.selectNavItem(1),
                ),
                _buildNavItem(
                  icon: 'shopping_cart',
                  label: 'Orders List',
                  isSelected: controller.selectedNavIndex.value == 2,
                  onTap: () => controller.selectNavItem(2),
                ),
              ],
            )),
          ),

          // Logout Button
          Padding(
            padding: EdgeInsets.all(MySize.size16),
            child: ListTile(
              leading: PlatformIcon(
                iconName: 'logout',
                size: MySize.size20,
                color: Colors.white,
              ),
              title: Text(
                'Logout',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: Colors.white,
                ),
              ),
              onTap: controller.logout,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.primaryColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.primaryColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),

          SizedBox(height: MySize.size32),

          // Dashboard Grid
          Expanded(
            child: _buildDashboardGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Welcome Text
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                      'Welcome Back Krishna',
                      style: TextStyle(
                        fontSize: MySize.size24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: MySize.size8),
                  Text(
                    '👋',
                    style: TextStyle(fontSize: MySize.size24),
                  ),
                ],
              ),
              SizedBox(height: MySize.size4),
              Text(
                "Let's get your orders moving",
                style: TextStyle(
                  fontSize: MySize.size16,
                  color: AppColors.textSecondary,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        SizedBox(width: MySize.size16),

        // Search Bar
        Expanded(
          flex: 1,
          child: Container(
            height: MySize.size40,
            constraints: BoxConstraints(
              maxWidth: 300,
              minWidth: 200,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(MySize.size20),
              border: Border.all(color: AppColors.borderColor),
            ),
            child: TextField(
              controller: controller.searchController,
              onChanged: controller.onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search Orders',
                hintStyle: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: MySize.size14,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                  size: MySize.size20,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size8,
                ),
              ),
            ),
          ),
        ),

        SizedBox(width: MySize.size16),

        // Refresh Button
        IconButton(
          onPressed: controller.refreshData,
          icon: Icon(
            Icons.refresh,
            color: AppColors.primaryColor,
            size: MySize.size24,
          ),
          tooltip: 'Refresh Dashboard',
        ),
      ],
    );
  }

  Widget _buildDashboardGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive crossAxisCount based on available width
        int crossAxisCount = 4;
        if (constraints.maxWidth < 800) {
          crossAxisCount = 2;
        } else if (constraints.maxWidth < 1200) {
          crossAxisCount = 3;
        }

        return GridView.count(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: MySize.size16,
          mainAxisSpacing: MySize.size16,
          childAspectRatio: 1.2,
      children: [
        _buildDashboardCard(
          title: 'Total Orders',
          count: '45',
          iconPath: 'assets/icons/onboarding_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'On-boarding',
          count: '5',
          iconPath: 'assets/icons/onboarding_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Designing',
          count: '5',
          iconPath: 'assets/icons/designing_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Sampling',
          count: '6',
          iconPath: 'assets/icons/sampling_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Design Plate Approved',
          count: '5',
          iconPath: 'assets/icons/design_plate_approval_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Cylinder Development',
          count: '5',
          iconPath: 'assets/icons/cylinder_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Polyester Sample Approved',
          count: '5',
          iconPath: 'assets/icons/polyster_sample_approved_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Polyester Printing',
          count: '1',
          iconPath: 'assets/icons/polyster_printing_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Lamination',
          count: '1',
          iconPath: 'assets/icons/lamination_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Metallised Pasting',
          count: '1',
          iconPath: 'assets/icons/pasting_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Heating',
          count: '1',
          iconPath: 'assets/icons/heating_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Curing',
          count: '1',
          iconPath: 'assets/icons/curing_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        // Add remaining cards
        _buildDashboardCard(
          title: 'Zipper Addition',
          count: '1',
          iconPath: 'assets/icons/zipper_addition_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Slitting',
          count: '1',
          iconPath: 'assets/icons/slitting_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Pouching',
          count: '1',
          iconPath: 'assets/icons/pouching_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Sorting',
          count: '1',
          iconPath: 'assets/icons/sorting_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Packing',
          count: '1',
          iconPath: 'assets/icons/packing_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Ready to Dispatch',
          count: '1',
          iconPath: 'assets/icons/ready_to_dispatch_icon.png',
          iconColor: AppColors.primaryColor,
        ),
        _buildDashboardCard(
          title: 'Dispatched',
          count: '1',
          iconPath: 'assets/icons/dispatched_icon.png',
          iconColor: AppColors.primaryColor,
        ),
      ],
        );
      },
    );
  }

  Widget _buildDashboardCard({
    required String title,
    required String count,
    required String iconPath,
    required Color iconColor,
  }) {
    return GestureDetector(
      onTap: () => controller.onCardTap(title, int.tryParse(count) ?? 0),
      child: Container(
        padding: EdgeInsets.all(MySize.size16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and Title Row
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  width: MySize.size32,
                  height: MySize.size32,
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                  child: Center(
                    child: SmartIcon(
                      assetPath: iconPath,
                      width: MySize.size20,
                      height: MySize.size20,
                      color: iconColor,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: MySize.size16),

            // Count
            Text(
              count,
              style: TextStyle(
                fontSize: MySize.size32,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}