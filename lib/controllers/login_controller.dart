import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';

class LoginController extends GetxController {
  // Text controllers
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();

  // Observable variables
  final agreeToPrivacyPolicy = false.obs;
  final isLoading = false.obs;

  // Form validation
  final formKey = GlobalKey<FormState>();

  @override
  void onClose() {
    phoneController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  // Toggle privacy policy agreement
  void togglePrivacyPolicy(bool? value) {
    agreeToPrivacyPolicy.value = value ?? false;
  }

  // Validate form
  bool get isFormValid {
    return phoneController.text.isNotEmpty &&
           passwordController.text.isNotEmpty &&
           agreeToPrivacyPolicy.value;
  }

  // Handle login
  Future<void> handleLogin() async {
    if (!isFormValid) {
      Get.snackbar(
        'Validation Error',
        'Please fill all fields and agree to privacy policy',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, accept any credentials
      // In real app, you would validate with your backend
      if (phoneController.text.isNotEmpty && passwordController.text.isNotEmpty) {
        // Show success message
        Get.snackbar(
          'Success',
          'Login successful!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate to dashboard
        Get.offAllNamed(AppRoutes.dashboard);
      } else {
        throw Exception('Invalid credentials');
      }
    } catch (e) {
      Get.snackbar(
        'Login Failed',
        'Invalid phone number or password',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Handle sign up navigation
  void handleSignUp() {
    Get.snackbar(
      'Info',
      'Sign up functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }
}
